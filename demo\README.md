# Accessibility Plugin Demo

This is a standalone demo version of the WordPress accessibility plugin that can run outside of the WordPress environment.

## 📁 Files Structure

```
demo/
├── accessibility-plugin-demo.html  # Main demo page
├── demo-functionality.js          # Simplified JavaScript for demo
└── README.md                      # This file
```

## 🚀 How to Use

1. **Open the demo**: Simply open `accessibility-plugin-demo.html` in any modern web browser
2. **Test the features**: Click the accessibility button in the bottom-right corner to open the widget
3. **Navigate through categories**: Use the main menu to access different feature categories:
   - **Text**: Font size, line spacing, dyslexic font, text-to-speech, reading guide
   - **Navigation**: ADHD focus mode, big cursor, text magnification
   - **Contrast & Colors**: Visual themes and custom color studio
   - **Preferences**: Widget position and language selection

## ✨ Features Included

### Text Category
- **Text to Speech**: Play/pause/stop controls with speed and voice selection
- **Font Size**: Adjustable from 80% to 200% with buttons and slider
- **Line Spacing**: Adjustable from 1.0 to 3.0 with buttons and slider
- **Dyslexic Font**: Toggle switch for dyslexia-friendly typography
- **Reading Guide**: Highlight current reading line

### Navigation Category
- **ADHD Focus Mode**: Reduce distractions for better focus
- **Big Cursor**: Larger, more visible cursor
- **Text Magnification**: Magnify text on hover

### Colors Category
- **Visual Themes**: 8 different themes including:
  - Default
  - Monochrome
  - Low/High Saturation
  - Dark Mode
  - High Contrast
  - Sepia
  - Color Blind Friendly
- **Color Studio**: Custom color picker for:
  - Text color
  - Background color
  - Link color
  - Heading color

### Preferences Category
- **Widget Position**: 4 position options (top-left, top-right, bottom-left, bottom-right)
- **Language Selection**: Multiple language options with flags

## 🎨 Design Features

- **Modern UI**: Clean, professional interface matching ThemeForest standards
- **Dark Theme**: Consistent with the plugin's design system
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Smooth Animations**: Elegant transitions and interactions
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation
- **Professional Icons**: High-quality SVG icons throughout

## 🔧 Technical Details

### CSS Architecture
The demo uses the original plugin's modular CSS files:
- `variables.css` - CSS custom properties and design tokens
- `widget-core.css` - Core widget styles
- `ui-components.css` - Reusable UI components
- `modal-system.css` - Modal and overlay system
- `category-menu.css` - Category navigation styles
- `text-features.css` - Text-related feature styles
- `navigation-features.css` - Navigation feature styles
- `color-features.css` - Color and theme styles
- `preferences.css` - Preferences screen styles
- `themes.css` - Visual theme definitions
- `animations.css` - Smooth transitions and animations
- `responsive.css` - Mobile and tablet responsive styles
- `utilities.css` - Utility classes

### JavaScript Functionality
The demo includes simplified JavaScript that provides:
- Widget open/close functionality
- Category navigation
- Feature toggle interactions
- Form controls (sliders, buttons, toggles)
- Theme switching
- Language selection
- Position changes
- Basic accessibility features (keyboard navigation, focus management)

## 🌟 Demo Content

The demo page includes sample content to test accessibility features:
- Headings and paragraphs for text-to-speech testing
- Links for color theme testing
- Various text elements for font size and spacing adjustments
- Content that responds to visual theme changes

## 🔗 Integration

This demo can be easily integrated into:
- Landing pages
- Product demonstrations
- Client presentations
- Marketing materials
- Documentation sites

## 📱 Browser Compatibility

The demo works in all modern browsers:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🎯 Use Cases

- **Product Demos**: Show potential customers how the plugin works
- **Client Presentations**: Demonstrate features without WordPress setup
- **Testing**: Test UI/UX changes before implementing in WordPress
- **Marketing**: Include in landing pages and promotional materials
- **Documentation**: Provide interactive examples in documentation

## 🚀 Next Steps

To integrate this demo into your landing page:
1. Copy the demo files to your landing page directory
2. Update the CSS file paths if needed
3. Customize the demo content to match your needs
4. Add any additional styling to match your brand

The demo is fully self-contained and ready to use!
