/**
 * Demo Functionality for Accessibility Plugin
 * Simplified version for standalone demo
 */

(function() {
    'use strict';

    class AccessibilityDemo {
        constructor() {
            this.isWidgetOpen = false;
            this.currentView = 'main-menu';
            this.init();
        }

        init() {
            this.bindEvents();
            this.setupInitialState();
        }

        bindEvents() {
            // Main toggle button
            const mainToggle = document.getElementById('map-main-toggle');
            if (mainToggle) {
                mainToggle.addEventListener('click', () => this.toggleWidget());
            }

            // Close button
            const closeButton = document.getElementById('map-close-button');
            if (closeButton) {
                closeButton.addEventListener('click', () => this.closeWidget());
            }

            // Back button
            const backButton = document.getElementById('map-back-button');
            if (backButton) {
                backButton.addEventListener('click', () => this.goBack());
            }

            // Category buttons
            const categoryButtons = document.querySelectorAll('.map-category-button');
            categoryButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const category = e.currentTarget.dataset.category;
                    if (category) {
                        this.showView(`map-view-${category}`);
                    }
                });
            });

            // Feature toggles
            const featureToggles = document.querySelectorAll('.map-feature-toggle');
            featureToggles.forEach(toggle => {
                toggle.addEventListener('click', (e) => {
                    this.handleFeatureToggle(e.currentTarget);
                });
            });

            // Toggle switches
            const toggleInputs = document.querySelectorAll('.map-toggle-input');
            toggleInputs.forEach(input => {
                input.addEventListener('change', (e) => {
                    this.handleToggleSwitch(e.target);
                });
            });

            // Position buttons
            const positionButtons = document.querySelectorAll('.map-position-btn');
            positionButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    this.handlePositionChange(e.currentTarget);
                });
            });

            // Language options
            const languageOptions = document.querySelectorAll('.map-language-option');
            languageOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    this.handleLanguageChange(e.currentTarget);
                });
            });

            // Theme dots
            const themeDots = document.querySelectorAll('.map-theme-dot');
            themeDots.forEach(dot => {
                dot.addEventListener('click', (e) => {
                    this.handleThemeChange(e.currentTarget);
                });
            });

            // Font size controls
            this.setupFontSizeControls();
            
            // Line spacing controls
            this.setupLineSpacingControls();

            // TTS controls
            this.setupTTSControls();

            // Backdrop click to close
            const backdrop = document.querySelector('.map-modal-backdrop');
            if (backdrop) {
                backdrop.addEventListener('click', () => this.closeWidget());
            }

            // Escape key to close
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isWidgetOpen) {
                    this.closeWidget();
                }
            });
        }

        setupInitialState() {
            // Hide all views except main menu
            const views = document.querySelectorAll('.map-modal-view');
            views.forEach(view => {
                if (view.id !== 'map-main-menu') {
                    view.classList.remove('active');
                }
            });
        }

        toggleWidget() {
            if (this.isWidgetOpen) {
                this.closeWidget();
            } else {
                this.openWidget();
            }
        }

        openWidget() {
            const widget = document.getElementById('map-widget-panel');
            const toggle = document.getElementById('map-main-toggle');
            
            if (widget && toggle) {
                widget.style.display = 'block';
                toggle.setAttribute('aria-expanded', 'true');
                this.isWidgetOpen = true;
                
                // Focus management
                setTimeout(() => {
                    const closeButton = document.getElementById('map-close-button');
                    if (closeButton) closeButton.focus();
                }, 100);
            }
        }

        closeWidget() {
            const widget = document.getElementById('map-widget-panel');
            const toggle = document.getElementById('map-main-toggle');
            
            if (widget && toggle) {
                widget.style.display = 'none';
                toggle.setAttribute('aria-expanded', 'false');
                this.isWidgetOpen = false;
                
                // Return to main menu
                this.showView('map-main-menu');
                
                // Return focus to toggle button
                toggle.focus();
            }
        }

        showView(viewId) {
            // Hide all views
            const views = document.querySelectorAll('.map-modal-view');
            views.forEach(view => view.classList.remove('active'));
            
            // Show target view
            const targetView = document.getElementById(viewId);
            if (targetView) {
                targetView.classList.add('active');
                this.currentView = viewId;
                
                // Update header
                this.updateHeader(viewId);
            }
        }

        updateHeader(viewId) {
            const title = document.getElementById('map-widget-title');
            const backButton = document.getElementById('map-back-button');
            
            if (viewId === 'map-main-menu') {
                if (title) title.textContent = 'Accessibility';
                if (backButton) backButton.style.display = 'none';
            } else {
                const view = document.getElementById(viewId);
                if (view && title) {
                    const viewTitle = view.dataset.title || 'Settings';
                    title.textContent = viewTitle;
                }
                if (backButton) backButton.style.display = 'block';
            }
        }

        goBack() {
            this.showView('map-main-menu');
        }

        handleFeatureToggle(toggle) {
            const isActive = toggle.dataset.active === 'true';
            const content = toggle.nextElementSibling;
            
            if (content && content.classList.contains('map-feature-controls')) {
                if (isActive) {
                    content.style.display = 'none';
                    toggle.dataset.active = 'false';
                } else {
                    content.style.display = 'block';
                    toggle.dataset.active = 'true';
                }
            }
        }

        handleToggleSwitch(input) {
            // Visual feedback for toggle switches
            console.log(`Toggle ${input.id}: ${input.checked}`);
        }

        handlePositionChange(button) {
            // Remove active class from all position buttons
            const positionButtons = document.querySelectorAll('.map-position-btn');
            positionButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            button.classList.add('active');
            
            console.log(`Position changed to: ${button.dataset.position}`);
        }

        handleLanguageChange(option) {
            // Remove active class from all language options
            const languageOptions = document.querySelectorAll('.map-language-option');
            languageOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            option.classList.add('active');
            
            console.log(`Language changed to: ${option.dataset.language}`);
        }

        handleThemeChange(dot) {
            // Remove active class from all theme dots
            const themeDots = document.querySelectorAll('.map-theme-dot');
            themeDots.forEach(d => d.classList.remove('active'));
            
            // Add active class to clicked dot
            dot.classList.add('active');
            
            // Update theme preview
            const preview = document.getElementById('map-theme-icon-preview');
            const themeName = document.getElementById('map-theme-name');
            
            if (preview) {
                preview.dataset.theme = dot.dataset.theme;
            }
            
            if (themeName) {
                const themeNames = {
                    'normal': 'Default',
                    'monochrome': 'Monochrome',
                    'low-saturation': 'Low Saturation',
                    'high-saturation': 'High Saturation',
                    'dark': 'Dark Mode',
                    'high-contrast': 'High Contrast',
                    'sepia': 'Sepia',
                    'colorblind': 'Color Blind Friendly'
                };
                themeName.textContent = themeNames[dot.dataset.theme] || 'Theme';
            }
            
            console.log(`Theme changed to: ${dot.dataset.theme}`);
        }

        setupFontSizeControls() {
            const increaseBtn = document.getElementById('map-font-increase');
            const decreaseBtn = document.getElementById('map-font-decrease');
            const slider = document.getElementById('map-font-size-slider');
            const display = document.getElementById('map-font-size-value');
            
            if (increaseBtn) {
                increaseBtn.addEventListener('click', () => {
                    const currentValue = parseInt(slider.value);
                    const newValue = Math.min(200, currentValue + 10);
                    slider.value = newValue;
                    display.textContent = newValue + '%';
                });
            }
            
            if (decreaseBtn) {
                decreaseBtn.addEventListener('click', () => {
                    const currentValue = parseInt(slider.value);
                    const newValue = Math.max(80, currentValue - 10);
                    slider.value = newValue;
                    display.textContent = newValue + '%';
                });
            }
            
            if (slider) {
                slider.addEventListener('input', (e) => {
                    display.textContent = e.target.value + '%';
                });
            }
        }

        setupLineSpacingControls() {
            const increaseBtn = document.getElementById('map-spacing-increase');
            const decreaseBtn = document.getElementById('map-spacing-decrease');
            const slider = document.getElementById('map-line-spacing-slider');
            const display = document.getElementById('map-line-spacing-value');
            
            if (increaseBtn) {
                increaseBtn.addEventListener('click', () => {
                    const currentValue = parseFloat(slider.value);
                    const newValue = Math.min(3, currentValue + 0.1);
                    slider.value = newValue.toFixed(1);
                    display.textContent = newValue.toFixed(1);
                });
            }
            
            if (decreaseBtn) {
                decreaseBtn.addEventListener('click', () => {
                    const currentValue = parseFloat(slider.value);
                    const newValue = Math.max(1, currentValue - 0.1);
                    slider.value = newValue.toFixed(1);
                    display.textContent = newValue.toFixed(1);
                });
            }
            
            if (slider) {
                slider.addEventListener('input', (e) => {
                    display.textContent = parseFloat(e.target.value).toFixed(1);
                });
            }
        }

        setupTTSControls() {
            const playBtn = document.getElementById('map-tts-play');
            const pauseBtn = document.getElementById('map-tts-pause');
            const stopBtn = document.getElementById('map-tts-stop');
            const speedSlider = document.getElementById('map-tts-speed');
            const speedDisplay = document.querySelector('.map-speed-display');
            
            if (playBtn) {
                playBtn.addEventListener('click', () => {
                    console.log('TTS Play clicked');
                    playBtn.style.display = 'none';
                    if (pauseBtn) pauseBtn.style.display = 'block';
                });
            }
            
            if (pauseBtn) {
                pauseBtn.addEventListener('click', () => {
                    console.log('TTS Pause clicked');
                    pauseBtn.style.display = 'none';
                    if (playBtn) playBtn.style.display = 'block';
                });
            }
            
            if (stopBtn) {
                stopBtn.addEventListener('click', () => {
                    console.log('TTS Stop clicked');
                    if (pauseBtn) pauseBtn.style.display = 'none';
                    if (playBtn) playBtn.style.display = 'block';
                });
            }
            
            if (speedSlider && speedDisplay) {
                speedSlider.addEventListener('input', (e) => {
                    speedDisplay.textContent = parseFloat(e.target.value).toFixed(1) + 'x';
                });
            }
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => new AccessibilityDemo());
    } else {
        new AccessibilityDemo();
    }
})();
