<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Accessibility Plugin - Premium WordPress Accessibility Solution</title>
    <meta name="description" content="Transform your WordPress website with our premium accessibility plugin. Features text-to-speech, contrast themes, dyslexic fonts, and more.">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="landing-page.css">

    <!-- Accessibility Plugin Styles -->
    <link rel="stylesheet" href="../assets/css/variables.css">
    <link rel="stylesheet" href="../assets/css/widget-core.css">
    <link rel="stylesheet" href="../assets/css/ui-components.css">
    <link rel="stylesheet" href="../assets/css/modal-system.css">
    <link rel="stylesheet" href="../assets/css/category-menu.css">
    <link rel="stylesheet" href="../assets/css/text-features.css">
    <link rel="stylesheet" href="../assets/css/navigation-features.css">
    <link rel="stylesheet" href="../assets/css/color-features.css">
    <link rel="stylesheet" href="../assets/css/preferences.css">
    <link rel="stylesheet" href="../assets/css/themes.css">
    <link rel="stylesheet" href="../assets/css/animations.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/utilities.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="#" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-universal-access"></i>
                </div>
                My Accessibility Plugin
            </a>
            
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#demo">Live Demo</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#support">Support</a></li>
            </ul>
            
            <a href="#" class="cta-button">
                <i class="fas fa-download"></i>
                Download Now
            </a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Make Your WordPress Site Accessible to Everyone
                </h1>
                <p class="hero-subtitle">
                    Transform your website with our premium accessibility plugin featuring text-to-speech, 
                    contrast themes, dyslexic fonts, and comprehensive accessibility tools.
                </p>
                <div class="hero-buttons">
                    <a href="#demo" class="cta-button">
                        <i class="fas fa-play"></i>
                        Try Live Demo
                    </a>
                    <a href="#features" class="btn-secondary">
                        <i class="fas fa-list"></i>
                        View Features
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Powerful Accessibility Features</h2>
            <p class="section-subtitle">
                Everything you need to make your WordPress website accessible and inclusive for all users.
            </p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-volume-up"></i>
                    </div>
                    <h3 class="feature-title">Text-to-Speech</h3>
                    <p class="feature-description">
                        Advanced text-to-speech functionality with customizable voice settings, 
                        reading speed control, and multi-language support for enhanced accessibility.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="feature-title">Contrast Themes</h3>
                    <p class="feature-description">
                        Multiple contrast themes including dark mode, high contrast, and colorblind-friendly 
                        options to improve readability for all users.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-font"></i>
                    </div>
                    <h3 class="feature-title">Dyslexic Font Support</h3>
                    <p class="feature-description">
                        OpenDyslexic font integration with customizable font sizes and spacing 
                        to help users with dyslexia read more comfortably.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h3 class="feature-title">Navigation Assistance</h3>
                    <p class="feature-description">
                        Enhanced navigation tools including focus indicators, keyboard navigation, 
                        and ADHD-friendly reading guides for better user experience.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="feature-title">Customizable Settings</h3>
                    <p class="feature-description">
                        Comprehensive admin panel with easy-to-use settings, user preferences 
                        storage, and seamless WordPress integration.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Fully Responsive</h3>
                    <p class="feature-description">
                        Mobile-first design that works perfectly on all devices and screen sizes, 
                        ensuring accessibility features are available everywhere.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="demo-section">
        <div class="container">
            <h2 class="section-title">Live Demo</h2>
            <p class="section-subtitle">
                Experience the plugin in action with our interactive demo.
            </p>

            <div class="demo-placeholder">
                <h3><i class="fas fa-play-circle"></i> Try the Accessibility Widget</h3>
                <p>Look for the purple accessibility button in the bottom-right corner of this page! Click it to explore all the features including text-to-speech, contrast themes, font adjustments, and more.</p>
                <div class="demo-features">
                    <div class="demo-feature">
                        <i class="fas fa-volume-up"></i>
                        <span>Text-to-Speech</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-palette"></i>
                        <span>Visual Themes</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-font"></i>
                        <span>Font Controls</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-cog"></i>
                        <span>Preferences</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Simple, Transparent Pricing</h2>
            <p class="section-subtitle">
                Choose the perfect plan for your WordPress website needs.
            </p>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Single Site</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">49</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 1 WordPress Site</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> 6 Months Support</li>
                        <li><i class="fas fa-check"></i> Regular Updates</li>
                        <li><i class="fas fa-check"></i> Documentation</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>

                <div class="pricing-card featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3 class="pricing-title">Developer</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">99</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited Sites</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> 12 Months Support</li>
                        <li><i class="fas fa-check"></i> Priority Updates</li>
                        <li><i class="fas fa-check"></i> Advanced Documentation</li>
                        <li><i class="fas fa-check"></i> White Label Rights</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Extended</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">199</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited Sites</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> Lifetime Support</li>
                        <li><i class="fas fa-check"></i> Priority Updates</li>
                        <li><i class="fas fa-check"></i> Source Code Access</li>
                        <li><i class="fas fa-check"></i> Custom Development</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-universal-access"></i>
                        </div>
                        <span>My Accessibility Plugin</span>
                    </div>
                    <p class="footer-description">
                        Making the web accessible for everyone with premium WordPress accessibility solutions.
                    </p>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Product</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#demo">Live Demo</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#">Documentation</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Bug Reports</a></li>
                        <li><a href="#">Feature Requests</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Legal</h4>
                    <ul class="footer-links">
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">License</a></li>
                        <li><a href="#">Refund Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 My Accessibility Plugin. All rights reserved.</p>
                <div class="footer-social">
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-github"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Accessibility Widget -->
    <div id="map-accessibility-widget" class="map-accessibility-widget">
        <!-- Main Toggle Button -->
        <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-label="Open accessibility tools">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="2"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 1 0 8 4 4 0 0 1 0-8z"/>
            </svg>
        </button>

        <!-- Widget Panel -->
        <div id="map-widget-panel" class="map-widget-panel" style="display: none;">
            <!-- Modal Backdrop -->
            <div class="map-modal-backdrop"></div>

            <!-- Modal Container -->
            <div class="map-modal-container">
                <!-- Modal Header -->
                <div class="map-modal-header">
                    <button id="map-back-button" class="map-back-button" type="button" aria-label="Go back" style="display: none;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 12H5"/>
                            <path d="M12 19l-7-7 7-7"/>
                        </svg>
                    </button>

                    <h2 id="map-widget-title" class="map-widget-title">Accessibility Tools</h2>

                    <button id="map-close-button" class="map-close-button" type="button" aria-label="Close accessibility tools">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6L6 18"/>
                            <path d="M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <!-- Modal Views Container -->
                <div class="map-modal-views-container">
                    <!-- Main Menu View -->
                    <div id="map-main-menu" class="map-modal-view active" role="region" aria-label="Accessibility categories">
                        <div class="map-view-content">
                            <div class="map-category-grid">
                                <!-- Text Category -->
                                <button class="map-category-button" data-category="text" type="button">
                                    <div class="map-category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                            <polyline points="10,9 9,9 8,9"/>
                                        </svg>
                                    </div>
                                    <div class="map-category-content">
                                        <div class="map-category-title">Text</div>
                                        <div class="map-category-desc">Reading and text options</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <!-- Contrast & Colors Category -->
                                <button class="map-category-button" data-category="colors" type="button">
                                    <div class="map-category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </div>
                                    <div class="map-category-content">
                                        <div class="map-category-title">Contrast & Colors</div>
                                        <div class="map-category-desc">Contrast and color themes</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <!-- Navigation Category -->
                                <button class="map-category-button" data-category="navigation" type="button">
                                    <div class="map-category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="3 11 22 2 13 21 11 13 3 11"/>
                                        </svg>
                                    </div>
                                    <div class="map-category-content">
                                        <div class="map-category-title">Navigation</div>
                                        <div class="map-category-desc">Keyboard shortcuts and navigation aids</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <!-- Preferences Category -->
                                <button class="map-category-button" data-category="preferences" type="button">
                                    <div class="map-category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 1 0 8 4 4 0 0 1 0-8z"/>
                                        </svg>
                                    </div>
                                    <div class="map-category-content">
                                        <div class="map-category-title">Preferences</div>
                                        <div class="map-category-desc">General settings and preferences</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Text Category View -->
                    <div id="map-view-text" class="map-modal-view" role="region" aria-label="Text accessibility options" data-title="Text">
                        <div class="map-view-content">
                            <!-- Text to Speech Section -->
                            <div class="map-feature-section">
                                <button id="map-text-to-speech-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5"/>
                                            <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Text to Speech</div>
                                        <div class="map-feature-desc">Listen to any text on the page</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-text-to-speech-checkbox" class="map-toggle-input">
                                        <label for="map-text-to-speech-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Font Size Section -->
                            <div class="map-feature-section">
                                <button id="map-font-size-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M4 7V4h16v3"/>
                                            <path d="M9 20h6"/>
                                            <path d="M12 4v16"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Font Size</div>
                                        <div class="map-feature-desc">Adjust text size for better readability</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-font-size-content" class="map-feature-controls" style="display: none;">
                                    <div class="map-font-size-controls">
                                        <div class="map-size-buttons">
                                            <button id="map-font-decrease" class="map-size-btn map-size-decrease" type="button" aria-label="Decrease font size">A-</button>
                                            <div class="map-size-display">
                                                <span id="map-font-size-value" class="map-size-value">100%</span>
                                            </div>
                                            <button id="map-font-increase" class="map-size-btn map-size-increase" type="button" aria-label="Increase font size">A+</button>
                                        </div>
                                        <div class="map-size-slider-container">
                                            <input type="range" id="map-font-size-slider" class="map-size-slider" min="80" max="200" step="10" value="100">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dyslexic Font Section -->
                            <div class="map-feature-section">
                                <button id="map-dyslexic-font-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                            <polyline points="10,9 9,9 8,9"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Dyslexic Font</div>
                                        <div class="map-feature-desc">Use dyslexia-friendly font</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-dyslexic-font-checkbox" class="map-toggle-input">
                                        <label for="map-dyslexic-font-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Reading Guide Section -->
                            <div class="map-feature-section">
                                <button id="map-reading-guide-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 3h18v18H3zM9 9h6v6H9z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Reading Guide</div>
                                        <div class="map-feature-desc">Highlight current reading line</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-reading-guide-checkbox" class="map-toggle-input">
                                        <label for="map-reading-guide-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Colors Category View -->
                    <div id="map-view-colors" class="map-modal-view" role="region" aria-label="Color accessibility options" data-title="Contrast & Colors">
                        <div class="map-view-content">
                            <!-- Visual Themes Section -->
                            <div class="map-feature-section">
                                <button id="map-contrast-themes-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Visual Themes</div>
                                        <div class="map-feature-desc">Choose a visual style that works best for you</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-contrast-themes-content" class="map-feature-controls" style="display: none;">
                                    <!-- Theme Selector Container -->
                                    <div class="map-theme-selector">
                                        <!-- Theme Preview Card -->
                                        <div class="map-theme-preview-card">
                                            <!-- Theme Icon Preview -->
                                            <div class="map-theme-icon-preview-container">
                                                <div id="map-theme-icon-preview" class="map-theme-icon-preview" data-theme="normal">
                                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                                    </svg>
                                                </div>
                                            </div>

                                            <!-- Theme Info -->
                                            <div class="map-theme-info">
                                                <h4 id="map-theme-name" class="map-theme-title">Default</h4>
                                            </div>
                                        </div>

                                        <!-- Theme Dots Indicator -->
                                        <div class="map-theme-dots">
                                            <button class="map-theme-dot active" data-theme="normal" aria-label="Apply default theme"></button>
                                            <button class="map-theme-dot" data-theme="monochrome" aria-label="Monochrome theme"></button>
                                            <button class="map-theme-dot" data-theme="low-saturation" aria-label="Low saturation theme"></button>
                                            <button class="map-theme-dot" data-theme="high-saturation" aria-label="High saturation theme"></button>
                                            <button class="map-theme-dot" data-theme="dark" aria-label="Dark theme"></button>
                                            <button class="map-theme-dot" data-theme="high-contrast" aria-label="High contrast theme"></button>
                                            <button class="map-theme-dot" data-theme="sepia" aria-label="Sepia theme"></button>
                                            <button class="map-theme-dot" data-theme="colorblind" aria-label="Color blind friendly theme"></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Category View -->
                    <div id="map-view-navigation" class="map-modal-view" role="region" aria-label="Navigation accessibility options" data-title="Navigation">
                        <div class="map-view-content">
                            <!-- ADHD Focus Mode Section -->
                            <div class="map-feature-section">
                                <button id="map-adhd-focus-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 1 0 8 4 4 0 0 1 0-8z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">ADHD Focus Mode</div>
                                        <div class="map-feature-desc">Reduce distractions and improve focus</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-adhd-focus-checkbox" class="map-toggle-input">
                                        <label for="map-adhd-focus-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Big Cursor Section -->
                            <div class="map-feature-section">
                                <button id="map-big-cursor-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="3,11 22,2 13,21 11,13 3,11"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Big Cursor</div>
                                        <div class="map-feature-desc">Larger, more visible cursor</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-big-cursor-checkbox" class="map-toggle-input">
                                        <label for="map-big-cursor-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Text Magnification Section -->
                            <div class="map-feature-section">
                                <button id="map-text-magnification-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="11" cy="11" r="8"/>
                                            <path d="M21 21l-4.35-4.35"/>
                                            <line x1="11" y1="8" x2="11" y2="14"/>
                                            <line x1="8" y1="11" x2="14" y2="11"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Text Magnification</div>
                                        <div class="map-feature-desc">Magnify text on hover</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-text-magnification-checkbox" class="map-toggle-input">
                                        <label for="map-text-magnification-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Category View -->
                    <div id="map-view-preferences" class="map-modal-view" role="region" aria-label="Preferences and settings" data-title="Preferences">
                        <div class="map-view-content">
                            <!-- Widget Position Section -->
                            <div class="map-feature-section">
                                <button id="map-widget-position-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Widget Position</div>
                                        <div class="map-feature-desc">Choose where to display the accessibility button</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-widget-position-content" class="map-feature-controls" style="display: none;">
                                    <!-- Position Grid -->
                                    <div class="map-position-grid">
                                        <button class="map-position-btn" data-position="top-left" type="button" aria-label="Position: Top Left">↖</button>
                                        <button class="map-position-btn" data-position="top-right" type="button" aria-label="Position: Top Right">↗</button>
                                        <button class="map-position-btn" data-position="bottom-left" type="button" aria-label="Position: Bottom Left">↙</button>
                                        <button class="map-position-btn active" data-position="bottom-right" type="button" aria-label="Position: Bottom Right">↘</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Language Section -->
                            <div class="map-feature-section">
                                <button id="map-language-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="10"/>
                                            <line x1="2" y1="12" x2="22" y2="12"/>
                                            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Language</div>
                                        <div class="map-feature-desc">Choose your preferred interface language</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-language-content" class="map-feature-controls" style="display: none;">
                                    <!-- Language Options -->
                                    <div class="map-language-options">
                                        <div class="map-language-option active" data-language="en">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇺🇸</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">English</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-language="fr">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇫🇷</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">French</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-language="es">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇪🇸</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Spanish</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- Close map-modal-views-container -->
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="landing-page.js"></script>
    <script src="../demo/demo-functionality.js"></script>
</body>
</html>
