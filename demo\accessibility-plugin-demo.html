<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Plugin Demo</title>
    
    <!-- Plugin CSS Files -->
    <link rel="stylesheet" href="../assets/css/variables.css">
    <link rel="stylesheet" href="../assets/css/widget-core.css">
    <link rel="stylesheet" href="../assets/css/ui-components.css">
    <link rel="stylesheet" href="../assets/css/modal-system.css">
    <link rel="stylesheet" href="../assets/css/category-menu.css">
    <link rel="stylesheet" href="../assets/css/text-features.css">
    <link rel="stylesheet" href="../assets/css/navigation-features.css">
    <link rel="stylesheet" href="../assets/css/color-features.css">
    <link rel="stylesheet" href="../assets/css/preferences.css">
    <link rel="stylesheet" href="../assets/css/themes.css">
    <link rel="stylesheet" href="../assets/css/animations.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/utilities.css">
    
    <!-- Demo Page Styles -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h1 {
            color: #7c3aed;
            margin-bottom: 10px;
        }
        
        .demo-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .demo-section h2 {
            color: #6d28d9;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .demo-text {
            margin-bottom: 20px;
        }
        
        .demo-links a {
            color: #7c3aed;
            text-decoration: none;
            margin-right: 15px;
        }
        
        .demo-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .demo-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .demo-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>Accessibility Plugin Demo</h1>
            <p>Test all accessibility features using the floating button in the bottom-right corner</p>
        </div>
        
        <div class="demo-content">
            <div class="demo-section">
                <h2>Sample Content</h2>
                <div class="demo-text">
                    <p>This is a demonstration of the accessibility plugin. You can test various features like text-to-speech, font size adjustment, line spacing, and visual themes.</p>
                    <p>The plugin provides comprehensive accessibility tools including dyslexic-friendly fonts, reading guides, ADHD focus mode, and custom color themes.</p>
                </div>
                
                <div class="demo-links">
                    <a href="#demo">Demo Link 1</a>
                    <a href="#test">Demo Link 2</a>
                    <a href="#sample">Demo Link 3</a>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>Features to Test</h2>
                <ul>
                    <li><strong>Text Features:</strong> Font size, line spacing, dyslexic font</li>
                    <li><strong>Reading Tools:</strong> Text-to-speech, reading guide, highlighting</li>
                    <li><strong>Navigation:</strong> ADHD focus mode, big cursor, text magnification</li>
                    <li><strong>Visual Themes:</strong> High contrast, dark mode, color customization</li>
                    <li><strong>Preferences:</strong> Widget position, language selection</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Additional Test Content</h2>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>

    <!-- Accessibility Widget -->
    <div id="map-accessibility-widget" class="map-accessibility-widget">
        <!-- Main Toggle Button -->
        <button id="map-main-toggle" class="map-main-toggle" type="button" aria-label="Open accessibility menu" aria-expanded="false">
            <div class="map-toggle-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                    <circle cx="8.5" cy="7" r="4"/>
                    <path d="M20 8v6"/>
                    <path d="M23 11h-6"/>
                </svg>
            </div>
        </button>

        <!-- Widget Panel -->
        <div id="map-widget-panel" class="map-widget-panel" role="dialog" aria-modal="true" aria-labelledby="map-widget-title" style="display: none;">
            <div class="map-modal-backdrop"></div>
            <div class="map-modal-container">
                <!-- Modal Header -->
                <div class="map-modal-header">
                    <div class="map-modal-header-content">
                        <button id="map-back-button" class="map-back-button" type="button" aria-label="Go back" style="display: none;">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M19 12H5"/>
                                <path d="M12 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <h2 id="map-widget-title" class="map-modal-title">Accessibility</h2>
                        <button id="map-close-button" class="map-close-button" type="button" aria-label="Close accessibility menu">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6L6 18"/>
                                <path d="M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Views Container -->
                <div class="map-modal-views-container">
                    <!-- Main Menu View -->
                    <div id="map-main-menu" class="map-modal-view active" role="region" aria-label="Main accessibility menu">
                        <div class="map-view-content">
                            <!-- Text Category -->
                            <button class="map-category-button" data-category="text" type="button">
                                <div class="map-category-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                        <polyline points="10,9 9,9 8,9"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Text</div>
                                    <div class="map-category-desc">Font size, spacing & readability</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Navigation Category -->
                            <button class="map-category-button" data-category="navigation" type="button">
                                <div class="map-category-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polygon points="3,11 22,2 13,21 11,13 3,11"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Navigation</div>
                                    <div class="map-category-desc">Cursor, focus & interaction tools</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Colors Category -->
                            <button class="map-category-button" data-category="colors" type="button">
                                <div class="map-category-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="13.5" cy="6.5" r=".5"/>
                                        <circle cx="17.5" cy="10.5" r=".5"/>
                                        <circle cx="8.5" cy="7.5" r=".5"/>
                                        <circle cx="6.5" cy="12.5" r=".5"/>
                                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Contrast & Colors</div>
                                    <div class="map-category-desc">Visual themes & color adjustments</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Preferences Category -->
                            <button class="map-category-button" data-category="preferences" type="button">
                                <div class="map-category-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 1 0 8 4 4 0 0 1 0-8z"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Preferences</div>
                                    <div class="map-category-desc">Position, language & settings</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Text Category View -->
                    <div id="map-view-text" class="map-modal-view" role="region" aria-label="Text accessibility options" data-title="Text">
                        <div class="map-view-content">
                            <!-- Text to Speech Section -->
                            <div class="map-feature-section">
                                <button id="map-text-to-speech-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5"/>
                                            <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Text to Speech</div>
                                        <div class="map-feature-desc">Listen to any text on the page</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-text-to-speech-content" class="map-feature-controls" style="display: none;">
                                    <!-- TTS Controls -->
                                    <div class="map-tts-controls">
                                        <div class="map-tts-main-controls">
                                            <button id="map-tts-play" class="map-tts-control-btn map-tts-play" type="button" aria-label="Start reading">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M8 5v14l11-7z"/>
                                                </svg>
                                            </button>
                                            <button id="map-tts-pause" class="map-tts-control-btn map-tts-pause" type="button" aria-label="Pause reading" style="display: none;">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M6 19h4V5H6v14zM14 5v14h4V5h-4z"/>
                                                </svg>
                                            </button>
                                            <button id="map-tts-stop" class="map-tts-control-btn map-tts-stop" type="button" aria-label="Stop reading">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M6 6h12v12H6z"/>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- Speed Control -->
                                        <div class="map-tts-speed-control">
                                            <label for="map-tts-speed" class="map-control-label">Speed</label>
                                            <div class="map-speed-slider-container">
                                                <span class="map-speed-label">0.5x</span>
                                                <input type="range" id="map-tts-speed" class="map-speed-slider" min="0.5" max="2" step="0.1" value="1">
                                                <span class="map-speed-label">2x</span>
                                            </div>
                                            <div class="map-speed-display">1x</div>
                                        </div>

                                        <!-- Voice Selection -->
                                        <div class="map-tts-voice-control">
                                            <label for="map-tts-voice" class="map-control-label">Voice</label>
                                            <select id="map-tts-voice" class="map-voice-select">
                                                <option value="">Default Voice</option>
                                            </select>
                                        </div>

                                        <!-- TTS Options -->
                                        <div class="map-tts-options">
                                            <label class="map-checkbox-label">
                                                <input type="checkbox" id="map-tts-highlight" class="map-checkbox">
                                                <span class="map-checkbox-custom"></span>
                                                <span class="map-checkbox-text">Highlight text while reading</span>
                                            </label>
                                            <label class="map-checkbox-label">
                                                <input type="checkbox" id="map-tts-auto-scroll" class="map-checkbox">
                                                <span class="map-checkbox-custom"></span>
                                                <span class="map-checkbox-text">Auto-scroll to follow reading</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Font Size Section -->
                            <div class="map-feature-section">
                                <button id="map-font-size-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M4 7V4h16v3"/>
                                            <path d="M9 20h6"/>
                                            <path d="M12 4v16"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Font Size</div>
                                        <div class="map-feature-desc">Adjust text size for better readability</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-font-size-content" class="map-feature-controls" style="display: none;">
                                    <div class="map-font-size-controls">
                                        <div class="map-size-buttons">
                                            <button id="map-font-decrease" class="map-size-btn map-size-decrease" type="button" aria-label="Decrease font size">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M4 7V4h16v3"/>
                                                    <path d="M9 20h6"/>
                                                    <path d="M12 4v16"/>
                                                </svg>
                                                <span class="map-size-label">A</span>
                                            </button>
                                            <div class="map-size-display">
                                                <span id="map-font-size-value" class="map-size-value">100%</span>
                                            </div>
                                            <button id="map-font-increase" class="map-size-btn map-size-increase" type="button" aria-label="Increase font size">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M4 7V4h16v3"/>
                                                    <path d="M9 20h6"/>
                                                    <path d="M12 4v16"/>
                                                </svg>
                                                <span class="map-size-label">A</span>
                                            </button>
                                        </div>
                                        <div class="map-size-slider-container">
                                            <input type="range" id="map-font-size-slider" class="map-size-slider" min="80" max="200" step="10" value="100">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Line Spacing Section -->
                            <div class="map-feature-section">
                                <button id="map-line-spacing-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 8h18"/>
                                            <path d="M3 12h18"/>
                                            <path d="M3 16h18"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Line Spacing</div>
                                        <div class="map-feature-desc">Increase space between lines</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-line-spacing-content" class="map-feature-controls" style="display: none;">
                                    <div class="map-line-spacing-controls">
                                        <div class="map-spacing-buttons">
                                            <button id="map-spacing-decrease" class="map-spacing-btn map-spacing-decrease" type="button" aria-label="Decrease line spacing">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M3 8h18"/>
                                                    <path d="M3 12h18"/>
                                                    <path d="M3 16h18"/>
                                                </svg>
                                            </button>
                                            <div class="map-spacing-display">
                                                <span id="map-line-spacing-value" class="map-spacing-value">1.5</span>
                                            </div>
                                            <button id="map-spacing-increase" class="map-spacing-btn map-spacing-increase" type="button" aria-label="Increase line spacing">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M3 8h18"/>
                                                    <path d="M3 12h18"/>
                                                    <path d="M3 16h18"/>
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="map-spacing-slider-container">
                                            <input type="range" id="map-line-spacing-slider" class="map-spacing-slider" min="1" max="3" step="0.1" value="1.5">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dyslexic Font Section -->
                            <div class="map-feature-section">
                                <button id="map-dyslexic-font-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                            <polyline points="10,9 9,9 8,9"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Dyslexic Font</div>
                                        <div class="map-feature-desc">Use dyslexia-friendly font</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-dyslexic-font-checkbox" class="map-toggle-input">
                                        <label for="map-dyslexic-font-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Reading Guide Section -->
                            <div class="map-feature-section">
                                <button id="map-reading-guide-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 3h18v18H3zM9 9h6v6H9z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Reading Guide</div>
                                        <div class="map-feature-desc">Highlight current reading line</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-reading-guide-checkbox" class="map-toggle-input">
                                        <label for="map-reading-guide-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Category View -->
                    <div id="map-view-navigation" class="map-modal-view" role="region" aria-label="Navigation accessibility options" data-title="Navigation">
                        <div class="map-view-content">
                            <!-- ADHD Focus Mode Section -->
                            <div class="map-feature-section">
                                <button id="map-adhd-focus-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 1 0 8 4 4 0 0 1 0-8z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">ADHD Focus Mode</div>
                                        <div class="map-feature-desc">Reduce distractions and improve focus</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-adhd-focus-checkbox" class="map-toggle-input">
                                        <label for="map-adhd-focus-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Big Cursor Section -->
                            <div class="map-feature-section">
                                <button id="map-big-cursor-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="3,11 22,2 13,21 11,13 3,11"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Big Cursor</div>
                                        <div class="map-feature-desc">Larger, more visible cursor</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-big-cursor-checkbox" class="map-toggle-input">
                                        <label for="map-big-cursor-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>

                            <!-- Text Magnification Section -->
                            <div class="map-feature-section">
                                <button id="map-text-magnification-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="11" cy="11" r="8"/>
                                            <path d="M21 21l-4.35-4.35"/>
                                            <line x1="11" y1="8" x2="11" y2="14"/>
                                            <line x1="8" y1="11" x2="14" y2="11"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Text Magnification</div>
                                        <div class="map-feature-desc">Magnify text on hover</div>
                                    </div>
                                    <div class="map-feature-toggle-switch">
                                        <input type="checkbox" id="map-text-magnification-checkbox" class="map-toggle-input">
                                        <label for="map-text-magnification-checkbox" class="map-toggle-label">
                                            <span class="map-toggle-slider"></span>
                                        </label>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Colors Category View -->
                    <div id="map-view-colors" class="map-modal-view" role="region" aria-label="Color accessibility options" data-title="Contrast & Colors">
                        <div class="map-view-content">
                            <!-- Visual Themes Section -->
                            <div class="map-feature-section">
                                <button id="map-contrast-themes-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Visual Themes</div>
                                        <div class="map-feature-desc">Choose a visual style that works best for you</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-contrast-themes-content" class="map-feature-controls" style="display: none;">
                                    <!-- Theme Selector Container -->
                                    <div class="map-theme-selector">
                                        <!-- Theme Preview Card -->
                                        <div class="map-theme-preview-card">
                                            <!-- Navigation Arrows -->
                                            <button id="map-theme-prev" class="map-theme-nav map-theme-nav-prev" type="button" aria-label="Previous theme (applies immediately)">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                                                </svg>
                                            </button>

                                            <button id="map-theme-next" class="map-theme-nav map-theme-nav-next" type="button" aria-label="Next theme (applies immediately)">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                                </svg>
                                            </button>

                                            <!-- Theme Icon Preview -->
                                            <div class="map-theme-icon-preview-container">
                                                <div id="map-theme-icon-preview" class="map-theme-icon-preview" data-theme="normal">
                                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                                    </svg>
                                                </div>
                                            </div>

                                            <!-- Theme Info -->
                                            <div class="map-theme-info">
                                                <h4 id="map-theme-name" class="map-theme-title">Default</h4>
                                            </div>
                                        </div>

                                        <!-- Theme Dots Indicator -->
                                        <div class="map-theme-dots">
                                            <button class="map-theme-dot active" data-theme="normal" aria-label="Apply default theme"></button>
                                            <button class="map-theme-dot" data-theme="monochrome" aria-label="Monochrome theme"></button>
                                            <button class="map-theme-dot" data-theme="low-saturation" aria-label="Low saturation theme"></button>
                                            <button class="map-theme-dot" data-theme="high-saturation" aria-label="High saturation theme"></button>
                                            <button class="map-theme-dot" data-theme="dark" aria-label="Dark theme"></button>
                                            <button class="map-theme-dot" data-theme="high-contrast" aria-label="High contrast theme"></button>
                                            <button class="map-theme-dot" data-theme="sepia" aria-label="Sepia theme"></button>
                                            <button class="map-theme-dot" data-theme="colorblind" aria-label="Color blind friendly theme"></button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Studio Section -->
                            <div class="map-feature-section">
                                <button id="map-custom-theme-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="13.5" cy="6.5" r=".5"/>
                                            <circle cx="17.5" cy="10.5" r=".5"/>
                                            <circle cx="8.5" cy="7.5" r=".5"/>
                                            <circle cx="6.5" cy="12.5" r=".5"/>
                                            <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Color Studio</div>
                                        <div class="map-feature-desc">Design your perfect color palette with live preview</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-custom-theme-content" class="map-feature-controls" style="display: none;">
                                    <!-- Color Studio Layout -->
                                    <div class="map-color-studio-compact">
                                        <!-- Text Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                        <polyline points="14,2 14,8 20,8"/>
                                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                                        <polyline points="10,9 9,9 8,9"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title">Text</span>
                                                    <span class="map-color-desc">Body text & paragraphs</span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-text-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-text-color" aria-label="Reset text color" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Background Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                                        <circle cx="8.5" cy="8.5" r="1.5"/>
                                                        <polyline points="21,15 16,10 5,21"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title">Background</span>
                                                    <span class="map-color-desc">Page & content areas</span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-bg-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-bg-color" aria-label="Reset background color" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Links Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title">Links</span>
                                                    <span class="map-color-desc">Hyperlinks & buttons</span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-link-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-link-color" aria-label="Reset link color" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Headings Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M6 12h12"/>
                                                        <path d="M6 20V4"/>
                                                        <path d="M18 20V4"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title">Headings</span>
                                                    <span class="map-color-desc">Titles & headers</span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-heading-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-heading-color" aria-label="Reset heading color" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Category View -->
                    <div id="map-view-preferences" class="map-modal-view" role="region" aria-label="Preferences and settings" data-title="Preferences">
                        <div class="map-view-content">
                            <!-- Widget Position Section -->
                            <div class="map-feature-section">
                                <button id="map-widget-position-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Widget Position</div>
                                        <div class="map-feature-desc">Choose where to display the accessibility button</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-widget-position-content" class="map-feature-controls" style="display: none;">
                                    <!-- Position Grid -->
                                    <div class="map-position-grid">
                                        <button class="map-position-btn" data-position="top-left" type="button" aria-label="Position: Top Left">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"/>
                                            </svg>
                                        </button>
                                        <button class="map-position-btn" data-position="top-right" type="button" aria-label="Position: Top Right">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"/>
                                            </svg>
                                        </button>
                                        <button class="map-position-btn" data-position="bottom-left" type="button" aria-label="Position: Bottom Left">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"/>
                                            </svg>
                                        </button>
                                        <button class="map-position-btn active" data-position="bottom-right" type="button" aria-label="Position: Bottom Right">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Language Section -->
                            <div class="map-feature-section">
                                <button id="map-language-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="10"/>
                                            <line x1="2" y1="12" x2="22" y2="12"/>
                                            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title">Language</div>
                                        <div class="map-feature-desc">Choose your preferred interface language</div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-language-content" class="map-feature-controls" style="display: none;">
                                    <!-- Language Options -->
                                    <div class="map-language-options">
                                        <div class="map-language-option active" data-language="en">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇺🇸</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">English</span>
                                                    <span class="map-language-native">English</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-language="fr">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇫🇷</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">French</span>
                                                    <span class="map-language-native">Français</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-language="es">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇪🇸</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Spanish</span>
                                                    <span class="map-language-native">Español</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-language="de">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">🇩🇪</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">German</span>
                                                    <span class="map-language-native">Deutsch</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- Close map-modal-views-container -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="demo-functionality.js"></script>
</body>
</html>
